import pandas as pd
import numpy as np
# from transformers import pipeline
from tqdm import tqdm
import boto3
import json

# Replace 'YOUR_ACCESS_KEY' and 'YOUR_SECRET_KEY' with your AWS access and secret keys
aws_access_key = '********************'
aws_secret_key = '3ChDbvjNTc9tqI4CR5S7teD1RBKVmfecA7MsLSW9'
bucket_name = 'sen-news'


VALID_LABELS = ['Clean Power',
        'Equity',
        'Debt',
        'Investments',
        'Finance',
        'Energy',
        'COP 29',
        'Bonds',
        'Carbon Markets',
        'ESG and Sustainability',
        'Nature Positive',
        'Policy and Regulation',
        'Circularity',
        'Oil and Gas',
        'Coal',
        'Real Estate',
        'Steel',
        'Transport',
        'Corporates',
        'Technology and Innovation',
        'Activists and Influencers',
        'Climate Scenario Analysis',
        'Events',
        'AI Investments',
        'Foundation Models (LLM Models)',
        'Infrastructure (Infra)',
        'Clean Energy and AI',
        'AI-Cybersecurity',
        'AI-Financial Institutions',
        'AI-Energy',
        'AI-Medical',
        'AI-Agriculture',
        'AI-Education',
        'AI-Transport',
        'Policy and Regulation in AI',
        'Hyperscalers',
        'AI Events',
        'CFPPs',
        'Natural Gas Power Plants',
        'Oil-Fired Power Plants',
        'Co-Firing Plants',
        'Intermittent Solar',
        'Intermittent Wind',
        'Intermittent Hybrid',
        'Baseload Nuclear Energy',
        'Baseload Geothermal',
        'Baseload Hydro Power',
        'Baseload Green Hydrogen',
        'Baseload WTE Power Plants',
        'Baseload Biomass Fired Power Plants',
        'Short Duration Storage',
        'Long Duration Storage',
        'Mechanical Storage',
        'Others']

# def clean_labels(labels_list):
#     """Clean and validate labels - keep all valid labels, remove only invalid ones"""
#     valid_labels = []
#     for label in labels_list:
#         label = str(label).strip()
#         if len(label) > 2 and label in VALID_LABELS:
#             valid_labels.append(label)
    
#     # Remove duplicates while preserving order
#     seen = set()
#     unique_valid_labels = []
#     for label in valid_labels:
#         if label not in seen:
#             seen.add(label)
#             unique_valid_labels.append(label)
    
#     return unique_valid_labels 


def get_labels_recursive(labels):
    file_key = 'google-alerts/jsons/2025-05-23-------05.json'
    s3 = boto3.client('s3', aws_access_key_id=aws_access_key, aws_secret_access_key=aws_secret_key)
    response = s3.get_object(Bucket=bucket_name, Key=file_key)
    json_content = json.loads(response['Body'].read().decode('utf-8'))
    data = pd.DataFrame(json_content)
    data = data
    # #model = SentenceTransformer('all-mpnet-base-v2')
    # model = pipeline("zero-shot-classification",
    #                  model="facebook/bart-large-mnli",device=0)
    # texts = data.keyword

    Sector = []
    final_scores = []
    threshold = 0.1
    current_labels = labels
    for _,i in tqdm(data.iterrows(),total=len(data)):
        text = i.body + ' '+ i.url + " " + i.keyword
        # current_labels = list(labels.keys())
        # loop_labels = labels
        final_label = None
        #print(current_labels)
        final_score = None
        try:
            final_score = 1
            final_label = get_label(text)
            print(i.keyword,final_label)
            # final_scores.append(final_score)
            temp = []
            # if len(final_label['labels']) > 10:
            #     continue
            final_label['labels'] = list(set(final_label['labels']))
            # final_label['labels'] = clean_labels(final_label['labels'])
            for label in final_label['labels']:
                temp.append({'Title':i.title, 'Url':i.url, 'Sector':label, 'importance_score':final_score})
            Sector += temp

        except Exception as e:
            print(e)
            # final_scores.append(1)
            # Sector.append('Others')


    # push_data = pd.DataFrame({'Title':data.title, 'Url':data.url, 'Sector':Sector, 'importance_score':final_scores})
    push_data = pd.DataFrame(Sector)
    # push_data.Sector = push_data.Sector.apply(lambda x: label_mapping[x])
    push_data.importance_score = push_data.importance_score.round(2)
    return push_data

# Example usage:
result_df = get_labels_recursive(labels)

#result_df = result_df[result_df['Sector'].isin(labels)]