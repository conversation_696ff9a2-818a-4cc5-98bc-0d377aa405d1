from langchain.output_parsers import RegexParser
from langchain_core.output_parsers import JsonOutputParser
from pydantic import BaseModel,Field
from pydantic import BaseModel
from typing import List
from typing import Literal

# Define the Pydantic model for labels
class DataRequired(BaseModel):
    labels: List[Literal[
        'Clean Power', 'Equity', 'Debt', 'Investments', 'Finance', 'Energy', 'COP 29', 'Bonds',
        'Carbon Markets', 'ESG and Sustainability', 'Nature Positive', 'Policy and Regulation',
        'Circularity', 'Oil and Gas', 'Coal', 'Real Estate', 'Steel', 'Transport', 'Corporates',
        'Technology and Innovation', 'Activists and Influencers', 'Climate Scenario Analysis',
        'Events', 'AI Investments', 'Foundation Models (LLM Models)', 'Infrastructure (Infra)',
        'Clean Energy and AI', 'AI-Cybersecurity', 'AI-Financial Institutions', 'AI-Energy',
        'AI-Medical', 'AI-Agriculture', 'AI-Education', 'AI-Transport', 'Policy and Regulation in AI',
        'Hyperscalers', 'AI Events', 'CFPPs', 'Natural Gas Power Plants', 'Oil-Fired Power Plants',
        'Co-Firing Plants', 'Intermittent Solar', 'Intermittent Wind', 'Intermittent Hybrid',
        'Baseload Nuclear Energy', 'Baseload Geothermal', 'Baseload Hydro Power',
        'Baseload Green Hydrogen', 'Baseload WTE Power Plants', 'Baseload Biomass Fired Power Plants',
        'Short Duration Storage', 'Long Duration Storage', 'Mechanical Storage', 'Others'
    ]]

# Define the list of labels
labels = [
    'Clean Power', 'Equity', 'Debt', 'Investments', 'Finance', 'Energy', 'COP 29', 'Bonds',
    'Carbon Markets', 'ESG and Sustainability', 'Nature Positive', 'Policy and Regulation',
    'Circularity', 'Oil and Gas', 'Coal', 'Real Estate', 'Steel', 'Transport', 'Corporates',
    'Technology and Innovation', 'Activists and Influencers', 'Climate Scenario Analysis',
    'Events', 'AI Investments', 'Foundation Models (LLM Models)', 'Infrastructure (Infra)',
    'Clean Energy and AI', 'AI-Cybersecurity', 'AI-Financial Institutions', 'AI-Energy',
    'AI-Medical', 'AI-Agriculture', 'AI-Education', 'AI-Transport', 'Policy and Regulation in AI',
    'Hyperscalers', 'AI Events', 'CFPPs', 'Natural Gas Power Plants', 'Oil-Fired Power Plants',
    'Co-Firing Plants', 'Intermittent Solar', 'Intermittent Wind', 'Intermittent Hybrid',
    'Baseload Nuclear Energy', 'Baseload Geothermal', 'Baseload Hydro Power',
    'Baseload Green Hydrogen', 'Baseload WTE Power Plants', 'Baseload Biomass Fired Power Plants',
    'Short Duration Storage', 'Long Duration Storage', 'Mechanical Storage', 'Others'
]

# Initialize the JSON output parser
output_parser = JsonOutputParser(pydantic_object=DataRequired)

def get_label(text):
    from langchain.prompts import PromptTemplate
    from langchain.chat_models import ChatOpenAI
    from langchain.chains import LLMChain
    import os

    # Updated prompt template with fixed output format and improved AI keyword handling
    prompt_template = """
    You are a classification model tasked with selecting the most appropriate label for the input text from the provided options. This is a multiclass classification task, so choose exactly one label that best matches the text. Follow these rules strictly:

    1. **AI-Related Text**: If the text contains keywords related to artificial intelligence (AI), it must be classified under one of the AI-specific labels (e.g., `AI-Medical`, `AI-Financial Institutions`, `AI-Transport`, etc.) based on the keywords provided. Check for exact or near-exact keyword matches first (e.g., "AI in healthcare" should map to `AI-Medical`). Do not use general labels like `Technology and Innovation` for AI-related text.
    2. **Clean Energy-Related Text**: If the text is related to clean energy, it must be classified under one of the clean energy-specific labels (e.g., `Clean Power`, `Intermittent Solar`, `Baseload Nuclear Energy`, etc.) based on the keywords provided.
    3. **Finance-Related Text**: Use the `Finance` label only for financing related to sustainability or clean energy. For stock market news, financial performance, or other unrelated finance topics (including general AI finance not specific to clean energy), use the `Others` label. Do not use `Investments` for stock-related news.
    4. **Keyword Matching**: Prioritize exact keyword matches or closely related terms. If no specific label matches, use `Others` for unrelated topics.
    5. **Output Format**: Return the result in the format: `label: <selected_label>`.

    **Label Descriptions and Keywords**:
    - **AI-Medical**: AI in healthcare, AI medical diagnostics, AI health tech, AI medical imaging, AI drug discovery, AI telemedicine, AI patient monitoring, AI in genomics, AI healthcare analytics, AI clinical decision support, AI in personalized medicine, AI medical robotics, AI in health data management, AI health monitoring, AI in medical research, AI for patient care, AI in hospital automation.
    - **AI-Financial Institutions**: AI in finance, AI banking solutions, AI financial analytics, AI fraud detection in banking, AI credit scoring, AI risk assessment, AI algorithmic trading, AI in wealth management, AI financial forecasting, AI loan processing, AI in fintech, AI banking automation, AI regulatory compliance, AI in financial services, AI payment processing, AI in insurance tech, AI for financial risk management.
    - **AI Investments**: AI investments, artificial intelligence funding, AI venture capital, AI financing, AI startup investments, AI investment trends 2025, AI funding rounds, AI mergers and acquisitions, AI seed funding, AI growth capital, AI tech investments, AI portfolio expansion, AI private equity, AI crowdfunding, AI R&D funding.
    - **Foundation Models (LLM Models)**: Large language models, foundation models, LLMs, generative AI models, OpenAI models, AI model scaling, multimodal AI models, transformer models, AI model training, pretrained AI models, neural network architectures, AI model deployment, generative AI research, AI model optimization, large-scale AI models.
    - **Infrastructure (Infra)**: AI infrastructure, AI data centers, AI computing resources, AI cloud parks, AI green data centers, AI cloud factories, AI hardware acceleration, GPU clusters for AI, AI chip design, AI server farms, AI edge computing, AI network optimization, AI computing scalability, AI data pipelines.
    - **Clean Energy and AI**: AI in clean energy, AI renewable energy, AI energy optimization, AI-driven energy efficiency, AI smart grids, AI energy management systems, AI in sustainable energy, AI for renewable integration, AI energy storage solutions, AI grid analytics, AI in green tech, AI for energy transition, AI in decarbonization.
    - **AI-Cybersecurity**: AI in cybersecurity, AI threat detection, AI security solutions, AI-driven threat intelligence, AI malware detection, AI network security, AI intrusion detection, AI cybersecurity automation, AI fraud prevention, AI endpoint security, AI in data protection, AI secure cloud systems.
    - **AI-Energy**: AI energy forecasting, AI utility management, AI energy consumption analytics, AI power grid optimization, AI energy demand prediction, AI in energy trading, AI for energy efficiency, AI load balancing, AI in energy infrastructure, AI energy monitoring.
    - **AI-Agriculture**: AI in agriculture, AI precision farming, AI crop monitoring, AI soil analysis, AI farm automation, AI agricultural drones, AI yield prediction, AI pest detection, AI irrigation optimization, AI in agtech, AI crop health monitoring, AI livestock management, AI in sustainable farming.
    - **AI-Education**: AI in education, AI learning platforms, AI educational tools, AI personalized learning, AI adaptive learning, AI virtual tutors, AI in e-learning, AI education analytics, AI student performance tracking, AI in educational content creation, AI classroom automation, AI learning management systems.
    - **AI-Transport**: AI in transportation, AI traffic management, AI autonomous vehicles, AI-powered cars, AI in fuel efficiency optimization, AI in eco-driving solutions, AI-powered electric vehicles, AI in hybrid vehicle efficiency, AI in Smart Logistics, Smart engine optimization, Smart EV battery management, AI-powered fuel efficiency, AI in fleet management, AI route optimization, AI in supply chain logistics, AI for autonomous drones, AI in smart mobility.
    - **Policy and Regulation in AI**: AI policy, AI regulations, AI governance, AI ethics, AI regulatory frameworks, AI compliance standards, AI policy developments, AI ethical guidelines, AI safety regulations, AI data privacy laws, AI transparency standards, AI accountability frameworks, AI regulatory compliance, AI ethical AI development.
    - **Hyperscalers**: AI hyperscalers, AI cloud providers, AI hyperscaler investments, AI cloud computing leaders, AI cloud infrastructure, AI large-scale computing, AI cloud service providers, AI data center expansion, AI hyperscale platforms, AI cloud scalability, AI enterprise cloud solutions.
    - **AI Events**: AI conferences, AI summits, AI workshops, AI trends, AI innovation events, AI tech expos, AI research symposiums, AI industry forums, AI hackathons, AI innovation summits, AI technology showcases, AI startup events.
    - **Clean Power**: Clean energy, sustainable power, green energy, renewable energy (not specific to other clean energy labels).
    - **Equity**: Investments in transition or renewable energy equity.
    - **Debt**: Investments in transition or renewable energy debt.
    - **Investments**: General investments related to sustainability or clean energy (not equity or debt).
    - **Finance**: Financing related to sustainability or clean energy (not covered by Equity, Debt, or Investments).
    - **Energy**: General energy topics not covered by specific clean energy labels.
    - **COP 29**: COP 29-related news.
    - **Bonds**: Bonds related to sustainability or clean energy (not covered by other labels).
    - **Carbon Markets**: Carbon markets, carbon credits, carbon trading.
    - **ESG and Sustainability**: ESG, sustainability topics not covered by other labels.
    - **Nature Positive**: Nature-positive initiatives, biodiversity.
    - **Policy and Regulation**: Policies and regulations related to energy, global warming, net-zero transition.
    - **Circularity**: Circular economy, carbon market circularity.
    - **Oil and Gas**: Oil and gas industry, fossil fuels.
    - **Coal**: Coal industry, coal mining, coal usage.
    - **Real Estate**: Real estate, property development.
    - **Steel**: Steel industry, steel production.
    - **Transport**: General transportation (not AI-related).
    - **Corporates**: Corporate sustainability, corporate responsibility.
    - **Technology and Innovation**: General technology and innovation (not AI or clean energy).
    - **Activists and Influencers**: Sustainability and environmental activists, influencers.
    - **Climate Scenario Analysis**: Climate scenario analysis, climate risk modeling.
    - **Events**: Climate or sustainability events (not AI-related).
    - **CFPPs**: Coal-fired power plants, coal power generation, thermal power plants, coal energy transition, coal plant emissions, coal phase-out, clean coal technology, retrofitting coal plants, coal carbon capture, high-efficiency low-emission coal.
    - **Natural Gas Power Plants**: LNG power generation, gas-fired power plants, combined cycle gas turbines (CCGT), gas peaker plants, gas-to-power, methane emissions, natural gas phase-out, gas-fired grid stability, gas plant efficiency.
    - **Oil-Fired Power Plants**: Oil-fired generators, petroleum-based energy, heavy fuel oil (HFO) power, diesel electricity generation, marine fuel power plants, off-grid diesel power, oil power plant emissions, fuel oil alternatives, diesel phase-out.
    - **Co-Firing Plants**: Biomass co-firing, hydrogen co-firing, ammonia co-firing, gas-coal hybrid power, dual-fuel power plants, renewable co-firing, clean fuel blending, transition fuels, decarbonizing coal plants, co-firing emissions reduction.
    - **Intermittent Solar**: Photovoltaic energy, solar farm reliability, CSP energy storage, solar grid integration, floating solar PV, AI in solar forecasting, peak solar generation, solar hybrid systems, seasonal solar variability, solar panels.
    - **Intermittent Wind**: Wind farm efficiency, onshore wind generation, offshore wind scalability, floating wind turbines, wind grid stability, wind energy forecasting, wind curtailment solutions, wind turbine storage integration, wind power market trends.
    - **Intermittent Hybrid**: Solar-wind hybrid plants, multi-source clean energy, hybrid energy storage, AI-optimized hybrid power, microgrid hybrid solutions, grid-connected hybrid renewables, hybrid system efficiency, wind-solar co-location, hybrid energy modeling.
    - **Baseload Nuclear Energy**: Small modular reactors (SMR), molten salt reactors (MSR), advanced nuclear reactors, pressurized water reactors (PWR), thorium fuel cycle, nuclear power reliability, grid-scale nuclear, fast breeder reactors (FBR), next-gen nuclear energy, nuclear energy.
    - **Baseload Geothermal**: Deep-earth energy, enhanced geothermal systems (EGS), geothermal heat extraction, hydrothermal resources, geothermal drilling technology, binary cycle geothermal plants, geothermal heat pumps, volcanic energy harvesting, geothermal grid integration, geothermal energy.
    - **Baseload Hydro Power**: Pumped storage hydro, run-of-river hydro, large-scale hydroelectricity, reservoir hydro power, dam-based hydro power, hydropower grid stability, hydro turbine efficiency, low-impact hydro projects, hydroelectric decarbonization, hydro power plants, pump storage hydro power.
    - **Baseload Green Hydrogen**: Hydrogen-fired electricity, electrolyzer efficiency, hydrogen fuel integration, hydrogen grid stability, hydrogen gas turbines, ammonia-to-hydrogen conversion, hydrogen energy storage, green hydrogen infrastructure, hydrogen blending for power.
    - **Baseload WTE Power Plants**: Municipal solid waste (MSW) power, incineration-based power generation, landfill gas electricity, anaerobic digestion energy, refuse-derived fuel (RDF) power, plasma gasification plants, WtE carbon footprint, circular economy energy, bio-waste electricity generation, waste-to-energy power plants, WTE power plants.
    - **Baseload Biomass Fired Power Plants**: Wood pellet power generation, agricultural waste biomass, biomass gasification, carbon-neutral biomass, CHP (combined heat and power) biomass, bioenergy carbon capture and storage (BECCS), forestry waste power, biogas-fired power, biomass supply chain, biomass fired power plants.
    - **Short Duration Storage**: Lithium-ion batteries, sodium-ion batteries, battery grid storage, fast-charging batteries, peak load battery support, short-duration energy discharge, battery management systems (BMS), distributed energy storage, rapid-response storage, grid-scale battery systems.
    - **Long Duration Storage**: Vanadium redox flow batteries (VRFB), sodium-sulfur (NaS) batteries, seasonal energy storage, long-duration battery efficiency, deep discharge storage, hydrogen energy storage, pumped thermal energy storage (PTES), compressed air energy storage (CAES), utility-scale battery deployment.
    - **Mechanical Storage**: Pumped hydro storage, gravity energy storage, kinetic energy storage, flywheel energy storage, rail energy storage, hydroelectric storage, liquid air energy storage (LAES), mechanical battery solutions, large-scale grid storage, rotational inertia storage.
    - **Others**: Stock market news, financial performance, or any text not matching the above labels.

    **Input Text**: {input_text}

    **Output Format**: label: {labels}
    """

    # Create the prompt
    prompt = PromptTemplate(
        input_variables=["input_text"],
        template=prompt_template
    )

    # Set up the LLM
    os.environ["OPENAI_API_KEY"] = "dummy_api_key"
    os.environ["OPENAI_API_BASE"] = "http://192.168.0.152:5000/v1"

    llm = ChatOpenAI(
        model="Qwen/Qwen2.5-Coder-7B-Instruct-GPTQ-Int8",
        temperature=0
    )

    # Create the chain
    chain = LLMChain(
        llm=llm,
        prompt=prompt,
        output_parser=output_parser
    )

    # Run the chain
    output = chain.run({
        "input_text": text
    })
    return output