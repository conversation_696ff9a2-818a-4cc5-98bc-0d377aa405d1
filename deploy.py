import requests
import json
import pandas as pd
from datetime import datetime
import pytz
import pymsteams
 
# --- CONFIGURATION ---
API_URL = "http://43.205.11.142:8000/api/v1/create-articles"
DATE_HOUR = "2025-05-23-13"  # For use in API payload
WEBHOOK_URL = "https://sustainabilityeconomics736.webhook.office.com/webhookb2/09b09f77-9689-4d6e-ab7b-7d3ada9eabcc@a8606430-fd02-4b66-b04e-e614589bb5ae/IncomingWebhook/6863eaf995d6449aba88b297813fe5d4/edf64186-ced7-41af-8555-dcbd1cb36c0a"
 
# --- DATA PREPARATION ---
# df = pd.read_csv(f'./files/csvs/modelv2_{DATE_HOUR}.csv')  # Optional CSV load
df = result_df.copy()
df.dropna(subset=["Title"], inplace=True)
df.reset_index(drop=True, inplace=True)
 
# --- REQUEST HEADERS ---
headers = {
    "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36",
    'Content-Type': 'application/json',
    "accept-language": "en-US,es;q=0.8"
}
 
# --- SEND MESSAGE TO TEAMS ---
def send_message(notebook_type):
    ist_time = datetime.utcnow().replace(tzinfo=pytz.utc).astimezone(pytz.timezone('Asia/Kolkata'))
    formatted_time = ist_time.strftime('%Y-%m-%d : : %H') + "H"
 
    message = pymsteams.connectorcard(WEBHOOK_URL)
    message.color("#F8C471")
 
    if notebook_type == 'Sustainability Economics News':
        message.title("Google Alerts")
        message.text(f"New articles are available for {formatted_time}")
        message.addLinkButton("Open SenTool", "http://sentool.duckdns.org:3000/login/")
    else:
        message.title("RSS Feeds")
        message.text(f"New articles are available for {formatted_time}")
        message.addLinkButton(
            "Open One-Note",
            "https://sustainabilityeconomics736-my.sharepoint.com/:o:/g/personal/alerts_sustainabilityeconomicsnews_com/Emmz3lh7sgZNuo5DQI1vNuABnd43iBHQ_kpRroaDbEJ8xg?e=4Btigh"
        )
 
    try:
        message.send()
        print("✅ Microsoft Teams message sent successfully.")
    except pymsteams.TeamsWebhookException as e:
        print("❌ Failed to send Teams message:", e)
 
# --- POST DATA TO API ---
for name, group in df.groupby('Sector'):
    print(f"📌 Sector: {name} | Articles: {len(group)}")
    print("=" * 80)
 
    for _, row in group.iterrows():
        payload = {
            "date": f"{DATE_HOUR}H",
            "title": row['Title'],
            "link": row['Url'],
            "sector": row['Sector'],
            "score": row['importance_score'],
            "priority": None,
            "assignee": None,
            "status": "Not Started",
            "comments": " ",
            "published": "No"
        }
 
        response = requests.post(API_URL, headers=headers, data=json.dumps(payload))
        print(response.text)
        print("Done & Dusted".center(100, "-"))
 
# --- TRIGGER TEAMS MESSAGE ---
send_message("Sustainability Economics News")
 
 